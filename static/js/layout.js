// 布局相关功能

class LayoutManager {
  constructor() {
    this.isCollapsed = false;
    this.isMobile = window.innerWidth <= 768;
    this.currentPage = this.getCurrentPage();
    this.init();
  }

  // 初始化布局
  init() {
    this.setupEventListeners();
    this.updateActiveMenu();
    this.updateBreadcrumb();
    this.handleResize();
  }

  // 获取当前页面
  getCurrentPage() {
    const pathname = window.location.pathname;
    if (pathname.includes('reports.html')) return 'reports';
    if (pathname.includes('records.html')) return 'records';
    if (pathname.includes('departments.html')) return 'departments';
    if (pathname.includes('dormitories.html')) return 'dormitories';
    if (pathname.includes('residents.html')) return 'residents';
    if (pathname.includes('config.html')) return 'config';
    return 'reports'; // 默认
  }

  // 设置事件监听器
  setupEventListeners() {
    // 侧边栏切换
    const toggleBtn = document.querySelector('.sidebar-toggle');
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => this.toggleSidebar());
    }

    // 用户下拉菜单
    const userInfo = document.querySelector('.user-info');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    if (userInfo && dropdownMenu) {
      userInfo.addEventListener('click', (e) => {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
      });

      // 点击其他地方关闭下拉菜单
      document.addEventListener('click', () => {
        dropdownMenu.classList.remove('show');
      });
    }

    // 刷新页面
    const refreshBtn = document.querySelector('.refresh-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        window.location.reload();
      });
    }

    // 窗口大小变化
    window.addEventListener('resize', () => this.handleResize());

    // 移动端侧边栏遮罩
    const overlay = document.querySelector('.sidebar-overlay');
    if (overlay) {
      overlay.addEventListener('click', () => this.closeMobileSidebar());
    }
  }

  // 切换侧边栏
  toggleSidebar() {
    if (this.isMobile) {
      this.toggleMobileSidebar();
    } else {
      this.toggleDesktopSidebar();
    }
  }

  // 桌面端侧边栏切换
  toggleDesktopSidebar() {
    const sidebar = document.querySelector('.app-sidebar');
    const toggleIcon = document.querySelector('.sidebar-toggle');
    
    this.isCollapsed = !this.isCollapsed;
    
    if (sidebar) {
      sidebar.classList.toggle('collapsed', this.isCollapsed);
    }
    
    if (toggleIcon) {
      toggleIcon.innerHTML = this.isCollapsed ? '📂' : '📁';
    }
  }

  // 移动端侧边栏切换
  toggleMobileSidebar() {
    const sidebar = document.querySelector('.app-sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar && overlay) {
      sidebar.classList.toggle('show');
      overlay.classList.toggle('show');
    }
  }

  // 关闭移动端侧边栏
  closeMobileSidebar() {
    const sidebar = document.querySelector('.app-sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (sidebar && overlay) {
      sidebar.classList.remove('show');
      overlay.classList.remove('show');
    }
  }

  // 处理窗口大小变化
  handleResize() {
    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;
    
    // 从桌面端切换到移动端
    if (!wasMobile && this.isMobile) {
      const sidebar = document.querySelector('.app-sidebar');
      if (sidebar) {
        sidebar.classList.remove('collapsed');
        sidebar.classList.remove('show');
      }
      this.isCollapsed = false;
    }
    
    // 从移动端切换到桌面端
    if (wasMobile && !this.isMobile) {
      const sidebar = document.querySelector('.app-sidebar');
      const overlay = document.querySelector('.sidebar-overlay');
      if (sidebar) sidebar.classList.remove('show');
      if (overlay) overlay.classList.remove('show');
    }
  }

  // 更新活动菜单
  updateActiveMenu() {
    const menuItems = document.querySelectorAll('.menu-item');
    menuItems.forEach(item => {
      const link = item.querySelector('a');
      if (link) {
        const href = link.getAttribute('href');
        if (href && href.includes(`${this.currentPage}.html`)) {
          item.classList.add('active');
        } else {
          item.classList.remove('active');
        }
      }
    });
  }

  // 更新面包屑
  updateBreadcrumb() {
    const breadcrumb = document.querySelector('.breadcrumb');
    if (!breadcrumb) return;

    const pageMap = {
      'reports': '报表统计',
      'records': '入住记录',
      'departments': '部门管理',
      'dormitories': '宿舍管理',
      'residents': '住户管理',
      'config': '系统配置'
    };

    const currentPageTitle = pageMap[this.currentPage] || '首页';
    
    breadcrumb.innerHTML = `
      <div class="breadcrumb-item">
        <a href="reports.html">首页</a>
      </div>
      ${this.currentPage !== 'reports' ? `
        <div class="breadcrumb-item">
          <a href="${this.currentPage}.html">${currentPageTitle}</a>
        </div>
      ` : ''}
    `;
  }
}

// 创建布局HTML结构的函数
function createLayout(pageContent, pageTitle) {
  return `
    <div class="app-layout">
      <!-- 侧边栏 -->
      <aside class="app-sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <h2>宿舍管理系统</h2>
          </div>
          <div class="logo-mini hidden">🏠</div>
        </div>
        
        <nav class="menu">
          <div class="menu-item">
            <a href="reports.html">
              <span class="menu-icon">📊</span>
              <span class="menu-title">报表统计</span>
            </a>
          </div>
          <div class="menu-item">
            <a href="records.html">
              <span class="menu-icon">📋</span>
              <span class="menu-title">入住记录</span>
            </a>
          </div>
          <div class="menu-item">
            <a href="departments.html">
              <span class="menu-icon">🏢</span>
              <span class="menu-title">部门管理</span>
            </a>
          </div>
          <div class="menu-item">
            <a href="dormitories.html">
              <span class="menu-icon">🏠</span>
              <span class="menu-title">宿舍管理</span>
            </a>
          </div>
          <div class="menu-item">
            <a href="residents.html">
              <span class="menu-icon">👤</span>
              <span class="menu-title">住户管理</span>
            </a>
          </div>
        </nav>
      </aside>

      <!-- 主内容区域 -->
      <div class="app-main-container">
        <!-- 顶部导航 -->
        <header class="app-header">
          <div class="header-left">
            <button class="sidebar-toggle">📁</button>
            <nav class="breadcrumb"></nav>
          </div>
          
          <div class="header-right">
            <button class="btn refresh-btn" title="刷新页面">🔄</button>

            <!-- 用户信息下拉菜单 -->
            <div class="user-info">
              <span>👤</span>
              <span class="username">用户</span>
              <span class="arrow-down">▼</span>
              
              <div class="dropdown-menu">
                <a href="#" class="dropdown-item" onclick="handleUserCommand('userInfo')">
                  <span>👤</span>
                  个人信息
                </a>
                <a href="#" class="dropdown-item divided" onclick="handleUserCommand('logout')">
                  <span>🚪</span>
                  退出登录
                </a>
              </div>
            </div>
          </div>
        </header>

        <!-- 主内容 -->
        <main class="app-main">
          ${pageContent}
        </main>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div class="sidebar-overlay"></div>
  `;
}

// 初始化布局的函数
function initLayout() {
  // 等待DOM加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new LayoutManager();
    });
  } else {
    new LayoutManager();
  }
}

// 导出到全局
window.LayoutManager = LayoutManager;
window.createLayout = createLayout;
window.initLayout = initLayout;

// 自动初始化布局（如果页面包含布局元素）
if (document.querySelector('.app-layout')) {
  initLayout();
}
