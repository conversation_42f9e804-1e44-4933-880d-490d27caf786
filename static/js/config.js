// 配置页面逻辑

class ConfigPageManager {
  constructor() {
    this.init();
  }

  init() {
    this.loadCurrentConfig();
    this.setupEventListeners();
    this.updateConfigDisplay();
  }

  // 加载当前配置
  loadCurrentConfig() {
    // 显示检测到的API路径
    const detectedPath = api.getApiBasePath();
    document.getElementById('detectedApiPath').value = detectedPath;

    // 加载保存的配置
    const savedApiPath = configManager.get('apiBasePath');
    const enableDebugLog = configManager.get('enableDebugLog');
    const requestTimeout = configManager.get('requestTimeout');

    // 设置表单值
    if (savedApiPath) {
      document.querySelector('input[name="pathMode"][value="manual"]').checked = true;
      document.getElementById('manualApiPath').value = savedApiPath;
      this.toggleManualPathGroup(true);
    } else {
      document.querySelector('input[name="pathMode"][value="auto"]').checked = true;
      this.toggleManualPathGroup(false);
    }

    document.getElementById('enableDebugLog').checked = enableDebugLog;
    document.getElementById('requestTimeout').value = requestTimeout;
  }

  // 设置事件监听器
  setupEventListeners() {
    // 路径模式切换
    const pathModeRadios = document.querySelectorAll('input[name="pathMode"]');
    pathModeRadios.forEach(radio => {
      radio.addEventListener('change', (e) => {
        this.toggleManualPathGroup(e.target.value === 'manual');
      });
    });
  }

  // 切换手动路径输入组的显示
  toggleManualPathGroup(show) {
    const group = document.getElementById('manualPathGroup');
    group.style.display = show ? 'block' : 'none';
  }

  // 保存API配置
  saveApiConfig() {
    const pathMode = document.querySelector('input[name="pathMode"]:checked').value;
    let apiBasePath = null;

    if (pathMode === 'manual') {
      apiBasePath = document.getElementById('manualApiPath').value.trim();
      if (!apiBasePath) {
        showMessage('请输入自定义API路径', 'error');
        return;
      }
      
      // 验证路径格式
      if (!apiBasePath.startsWith('/')) {
        showMessage('API路径必须以 / 开头', 'error');
        return;
      }
    }

    // 保存配置
    configManager.set('apiBasePath', apiBasePath);
    
    // 更新API客户端
    if (apiBasePath) {
      api.setApiBasePath(apiBasePath);
    } else {
      // 重新检测
      const newBasePath = api.detectApiBasePath();
      api.setApiBasePath(newBasePath);
      document.getElementById('detectedApiPath').value = newBasePath;
    }

    this.updateConfigDisplay();
    showMessage('API配置保存成功', 'success');
  }

  // 测试API连接
  async testApiConnection() {
    try {
      showMessage('正在测试API连接...', 'info');
      
      // 尝试调用健康检查接口
      const response = await api.get('/health', { loading: false });
      
      if (response) {
        showMessage('API连接测试成功', 'success');
      } else {
        showMessage('API连接测试失败：无响应数据', 'error');
      }
    } catch (error) {
      console.error('API连接测试失败:', error);
      showMessage(`API连接测试失败：${error.message}`, 'error');
    }
  }

  // 重置API配置
  async resetApiConfig() {
    const confirmed = await showConfirm('确定要重置API配置吗？这将清除所有自定义设置。', '重置确认');
    if (!confirmed) return;

    // 重置配置
    configManager.set('apiBasePath', null);
    
    // 重新检测API路径
    const newBasePath = api.detectApiBasePath();
    api.setApiBasePath(newBasePath);
    
    // 更新界面
    document.querySelector('input[name="pathMode"][value="auto"]').checked = true;
    document.getElementById('manualApiPath').value = '';
    document.getElementById('detectedApiPath').value = newBasePath;
    this.toggleManualPathGroup(false);
    this.updateConfigDisplay();
    
    showMessage('API配置已重置', 'success');
  }

  // 保存其他配置
  saveOtherConfig() {
    const enableDebugLog = document.getElementById('enableDebugLog').checked;
    const requestTimeout = parseInt(document.getElementById('requestTimeout').value);

    // 验证超时时间
    if (isNaN(requestTimeout) || requestTimeout < 5000 || requestTimeout > 60000) {
      showMessage('请求超时时间必须在5000-60000毫秒之间', 'error');
      return;
    }

    // 保存配置
    configManager.set('enableDebugLog', enableDebugLog);
    configManager.set('requestTimeout', requestTimeout);

    this.updateConfigDisplay();
    showMessage('配置保存成功', 'success');
  }

  // 更新配置信息显示
  updateConfigDisplay() {
    const configInfo = document.getElementById('configInfo');
    const config = configManager.config;
    
    configInfo.innerHTML = `
      <div class="descriptions">
        <div class="descriptions-item">
          <div class="descriptions-label">当前API基础路径</div>
          <div class="descriptions-content">
            <code>${api.getApiBasePath()}</code>
          </div>
        </div>
        <div class="descriptions-item">
          <div class="descriptions-label">路径配置模式</div>
          <div class="descriptions-content">
            ${config.apiBasePath ? '手动设置' : '自动检测'}
          </div>
        </div>
        <div class="descriptions-item">
          <div class="descriptions-label">调试日志</div>
          <div class="descriptions-content">
            ${config.enableDebugLog ? '已启用' : '已禁用'}
          </div>
        </div>
        <div class="descriptions-item">
          <div class="descriptions-label">请求超时时间</div>
          <div class="descriptions-content">
            ${config.requestTimeout}毫秒
          </div>
        </div>
        <div class="descriptions-item">
          <div class="descriptions-label">当前页面路径</div>
          <div class="descriptions-content">
            <code>${window.location.pathname}</code>
          </div>
        </div>
        <div class="descriptions-item">
          <div class="descriptions-label">访问方式</div>
          <div class="descriptions-content">
            ${this.getAccessType()}
          </div>
        </div>
      </div>
    `;
  }

  // 获取访问方式描述
  getAccessType() {
    const currentPath = window.location.pathname;
    const proxyPrefixMatch = currentPath.match(/^(\/[^\/]+)\/static\//);
    
    if (proxyPrefixMatch) {
      return `代理访问 (前缀: ${proxyPrefixMatch[1]})`;
    } else {
      return '直接访问';
    }
  }
}

// 全局函数
function saveApiConfig() {
  if (window.configPageManager) {
    window.configPageManager.saveApiConfig();
  }
}

function testApiConnection() {
  if (window.configPageManager) {
    window.configPageManager.testApiConnection();
  }
}

function resetApiConfig() {
  if (window.configPageManager) {
    window.configPageManager.resetApiConfig();
  }
}

function saveOtherConfig() {
  if (window.configPageManager) {
    window.configPageManager.saveOtherConfig();
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  window.configPageManager = new ConfigPageManager();
});
