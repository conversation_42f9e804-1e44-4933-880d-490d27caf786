/* 组件样式 - 替代Element Plus组件 */

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-text {
  color: white;
  font-size: 14px;
}

/* 对话框 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.dialog {
  background: white;
  border-radius: 8px;
  min-width: 420px;
  max-width: 90vw;
  box-shadow: var(--box-shadow-dark);
}

.dialog-header {
  padding: 20px 20px 10px;
  border-bottom: 1px solid var(--border-color-lighter);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color-primary);
}

.dialog-body {
  padding: 20px;
}

.dialog-footer {
  padding: 10px 20px 20px;
  border-top: 1px solid var(--border-color-lighter);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 下拉选择器 */
.select {
  position: relative;
  display: inline-block;
  width: 100%;
}

.select-input {
  position: relative;
  cursor: pointer;
}

.select-input .input__inner {
  cursor: pointer;
  padding-right: 35px;
}

.select-input::after {
  content: '';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid var(--text-color-placeholder);
  transition: transform 0.3s;
}

.select.is-focus .select-input::after {
  transform: translateY(-50%) rotate(180deg);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius-base);
  box-shadow: var(--box-shadow-light);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.select-dropdown.show {
  display: block;
}

.select-option {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.select-option:hover {
  background-color: #f5f7fa;
}

.select-option.selected {
  background-color: #ecf5ff;
  color: var(--primary-color);
}

/* 日期选择器 */
.date-picker {
  position: relative;
  display: inline-block;
  width: 100%;
}

.date-picker-input {
  position: relative;
  cursor: pointer;
}

.date-picker-input .input__inner {
  cursor: pointer;
  padding-right: 35px;
}

.date-picker-input::after {
  content: '📅';
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  color: var(--text-color-placeholder);
}

/* 分页器 */
.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px 0;
}

.pagination-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid var(--border-color-base);
  border-radius: var(--border-radius-base);
  background: white;
  color: var(--text-color-regular);
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
}

.pagination-item:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-item.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pagination-item:disabled {
  cursor: not-allowed;
  color: var(--text-color-placeholder);
  border-color: var(--border-color-lighter);
}

.pagination-info {
  color: var(--text-color-secondary);
  font-size: 13px;
  margin: 0 12px;
}

/* 标签 */
.tag {
  display: inline-block;
  height: 24px;
  padding: 0 8px;
  font-size: 12px;
  line-height: 22px;
  color: var(--text-color-primary);
  border: 1px solid var(--border-color-light);
  border-radius: 4px;
  background-color: var(--bg-color);
  white-space: nowrap;
}

.tag.tag-primary {
  color: var(--primary-color);
  background-color: #ecf5ff;
  border-color: #b3d8ff;
}

.tag.tag-success {
  color: var(--success-color);
  background-color: #f0f9ff;
  border-color: #c6f7d0;
}

.tag.tag-warning {
  color: var(--warning-color);
  background-color: #fdf6ec;
  border-color: #f5dab1;
}

.tag.tag-danger {
  color: var(--danger-color);
  background-color: #fef0f0;
  border-color: #fbc4c4;
}

.tag.tag-info {
  color: var(--info-color);
  background-color: #f4f4f5;
  border-color: #e9e9eb;
}

/* 徽章 */
.badge {
  position: relative;
  display: inline-block;
}

.badge-content {
  position: absolute;
  top: 0;
  right: 0;
  transform: translate(50%, -50%);
  background: var(--danger-color);
  color: white;
  font-size: 12px;
  line-height: 1;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
  white-space: nowrap;
}

/* 进度条 */
.progress {
  position: relative;
  line-height: 1;
}

.progress-bar {
  width: 100%;
  height: 6px;
  border-radius: 100px;
  background-color: var(--border-color-lighter);
  overflow: hidden;
  position: relative;
}

.progress-bar-inner {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 100px;
  transition: width 0.6s ease;
}

.progress-text {
  font-size: 14px;
  color: var(--text-color-regular);
  display: inline-block;
  vertical-align: middle;
  margin-left: 10px;
  line-height: 1;
}

/* 开关 */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--border-color-base);
  transition: 0.4s;
  border-radius: 20px;
}

.switch-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.switch-input:checked + .switch-slider {
  background-color: var(--primary-color);
}

.switch-input:checked + .switch-slider:before {
  transform: translateX(20px);
}

/* 骨架屏 */
.skeleton {
  display: block;
  width: 100%;
  height: 16px;
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton.skeleton-title {
  height: 20px;
  width: 40%;
}

.skeleton.skeleton-paragraph {
  height: 16px;
}

.skeleton.skeleton-paragraph:nth-child(2) {
  width: 61%;
}

.skeleton.skeleton-paragraph:nth-child(3) {
  width: 61%;
}

.skeleton.skeleton-paragraph:last-child {
  width: 61%;
  margin-bottom: 0;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 空状态 */
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-color-secondary);
}

.empty-image {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-description {
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .table {
    min-width: 600px;
  }
}

/* 配置页面样式 */
.config-info {
  background: var(--bg-color-light);
  border-radius: 8px;
  padding: 16px;
}

.descriptions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.descriptions-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.descriptions-label {
  min-width: 120px;
  font-weight: 500;
  color: var(--text-color-secondary);
  flex-shrink: 0;
}

.descriptions-content {
  flex: 1;
  color: var(--text-color);
}

.descriptions-content code {
  background: var(--bg-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
}

.radio-group {
  display: flex;
  gap: 16px;
}

.radio {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
}

.radio input[type="radio"] {
  margin: 0;
}

.radio-label {
  font-size: 14px;
  color: var(--text-color);
}

.info-box {
  background: var(--bg-color-light);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
}

.info-box:last-child {
  margin-bottom: 0;
}

.info-box h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
}

.info-box p {
  margin: 4px 0;
  font-size: 13px;
  color: var(--text-color-secondary);
}

.info-box code {
  background: var(--bg-color);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.readonly {
  background-color: var(--bg-color-light) !important;
  color: var(--text-color-secondary) !important;
  cursor: not-allowed !important;
}
