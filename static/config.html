<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - 宿舍入住管理系统</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/layout.css">
    <link rel="stylesheet" href="css/components.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-brand">
            <h1>宿舍入住管理系统</h1>
        </div>
        <div class="navbar-user">
            <div class="user-dropdown">
                <span class="username">用户</span>
                <div class="dropdown-content">
                    <a href="#" onclick="handleUserCommand('userInfo')">个人信息</a>
                    <a href="#" onclick="handleUserCommand('logout')">退出登录</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- 侧边栏 -->
    <aside class="sidebar">
        <ul class="sidebar-menu">
            <li><a href="reports.html">报表统计</a></li>
            <li><a href="records.html">入住记录</a></li>
            <li><a href="departments.html">部门管理</a></li>
            <li><a href="dormitories.html">宿舍管理</a></li>
            <li><a href="residents.html">住户管理</a></li>
            <li class="active"><a href="config.html">系统配置</a></li>
        </ul>
    </aside>

    <!-- 主内容区 -->
    <main class="main-content">
        <div class="page-header">
            <h2>系统配置</h2>
            <p>管理系统的基础配置和API访问设置</p>
        </div>

        <div class="content-wrapper">
            <!-- API配置卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>API访问配置</h3>
                    <p>配置前端访问后端API的基础路径</p>
                </div>
                <div class="card-body">
                    <form id="apiConfigForm" class="form">
                        <div class="form-item">
                            <label>当前检测到的API路径</label>
                            <div class="form-item__content">
                                <input type="text" id="detectedApiPath" readonly class="readonly">
                                <small class="form-item__help">系统自动检测的API基础路径</small>
                            </div>
                        </div>

                        <div class="form-item">
                            <label>API路径配置模式</label>
                            <div class="form-item__content">
                                <div class="radio-group">
                                    <label class="radio">
                                        <input type="radio" name="pathMode" value="auto" checked>
                                        <span class="radio-label">自动检测</span>
                                    </label>
                                    <label class="radio">
                                        <input type="radio" name="pathMode" value="manual">
                                        <span class="radio-label">手动设置</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-item" id="manualPathGroup" style="display: none;">
                            <label for="manualApiPath">自定义API路径</label>
                            <div class="form-item__content">
                                <input type="text" id="manualApiPath" name="manualApiPath" placeholder="例如: /bedsharing/api">
                                <small class="form-item__help">手动指定API基础路径，通常用于代理访问场景</small>
                            </div>
                        </div>

                        <div class="form-item">
                            <label>访问场景说明</label>
                            <div class="form-item__content">
                                <div class="info-box">
                                    <h4>直接访问</h4>
                                    <p>直接通过IP或域名访问，API路径为: <code>/api</code></p>
                                    <p>示例: <code>http://localhost:8000/api/v1/users</code></p>
                                </div>
                                <div class="info-box">
                                    <h4>Nginx代理访问</h4>
                                    <p>通过Nginx代理访问，API路径需要添加代理前缀</p>
                                    <p>示例: <code>http://domain.com/bedsharing/api/v1/users</code></p>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="saveApiConfig()">保存配置</button>
                            <button type="button" class="btn" onclick="testApiConnection()">测试连接</button>
                            <button type="button" class="btn btn-warning" onclick="resetApiConfig()">重置配置</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 其他配置卡片 -->
            <div class="card">
                <div class="card-header">
                    <h3>其他配置</h3>
                    <p>系统的其他配置选项</p>
                </div>
                <div class="card-body">
                    <form id="otherConfigForm" class="form">
                        <div class="form-item">
                            <label>
                                <input type="checkbox" id="enableDebugLog" name="enableDebugLog">
                                启用调试日志
                            </label>
                            <small class="form-item__help">在浏览器控制台显示详细的调试信息</small>
                        </div>

                        <div class="form-item">
                            <label for="requestTimeout">请求超时时间（毫秒）</label>
                            <div class="form-item__content">
                                <input type="number" id="requestTimeout" name="requestTimeout" min="5000" max="60000" step="1000">
                                <small class="form-item__help">API请求的超时时间，建议设置为30000毫秒</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-primary" onclick="saveOtherConfig()">保存配置</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 配置信息显示 -->
            <div class="card">
                <div class="card-header">
                    <h3>当前配置信息</h3>
                </div>
                <div class="card-body">
                    <div id="configInfo" class="config-info">
                        <!-- 配置信息将通过JavaScript动态填充 -->
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 加载必要的脚本 -->
    <script src="js/utils.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/layout.js"></script>
    <script src="js/config.js"></script>
</body>
</html>
