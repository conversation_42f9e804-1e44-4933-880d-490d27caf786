version: '3.8'

services:
  # 数据库服务
  db:
    image: mysql:8.0
    container_name: Dormitory_management_db
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: dormitory_management
    ports:
      - "3306:3306"
    volumes:
      # MySQL 数据持久化
      - mysql_data:/var/lib/mysql
      # MySQL 日志
      - mysql_logs:/var/log/mysql

      # 数据库初始化脚本
      - ./mysql/init:/docker-entrypoint-initdb.d:ro

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: Dormitory_management
    ports:
      - "18005:8005"
    environment:
      # 数据库配置
      - DATABASE_URL=mysql+pymysql://root:root@db:3306/dormitory_management
      - DATABASE_ECHO=false

      # 应用配置
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8005
      - LOG_LEVEL=INFO

      # 安全配置
      - SECRET_KEY=wangzhixin666666666
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=30
      # LDAP配置
      - LDAP_URI=ldap://**************:389
      - LDAP_BASE_DN_USERS=dc=users,dc=appdata,dc=erayt,dc=com
      - LDAP_CONNECT_TIMEOUT=3
      # 业务配置
      - UNIT_COST_PER_BED_DAY=100.0
    volumes:
      # 应用日志持久化
      - ./logs:/app/logs
    depends_on:
      - db

# 定义命名卷
volumes:
  # MySQL 数据持久化卷
  mysql_data:
    driver: local
  # MySQL 日志卷
  mysql_logs:
    driver: local

